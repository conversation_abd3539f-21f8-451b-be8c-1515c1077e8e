
import os
import json
from django.core.management.base import BaseCommand
from dj_language.models import Language
from apps.quran.models import QuranSura, QuranVerse, QuranWordTranslation
from django.db import transaction

import re

def detailed_correct_arabic_text(arabic_text):
    corrected_text = arabic_text.replace('ٱ', 'ا')
    corrected_text = corrected_text.replace('ۡ', '')  # Removing unnecessary sukoon marks
    corrected_text = corrected_text.replace('ٱل', 'ال')  # Removing unnecessary hamza above
    return corrected_text

class Command(BaseCommand):
    help = 'Import word translations from JSON data'

    def handle(self, *args, **options):

        file_path = os.path.join(os.path.dirname(__file__), 'main.json')

        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
            
        language = Language.objects.get(code='en')
        self.stdout.write(self.style.SUCCESS(f'Successfully {language}'))

        with transaction.atomic():
            
            for verse in data['verses']:
                try:
                    sura = QuranSura.objects.get(index=verse['surah_number'])
                    quran_verse = QuranVerse.objects.get(sura=sura, number_in_surah=verse['ayah_number'])
                    self.stdout.write(self.style.SUCCESS(f'sura: {sura} / verse: {quran_verse}'))
    
                    clean_text = detailed_correct_arabic_text(verse['arabic'])
                    QuranWordTranslation.objects.create(
                        verse=quran_verse,
                        arabic=clean_text,
                        translate=verse['english'],
                        language=language
                    )
                    self.stdout.write(self.style.SUCCESS(f'Successfully added translation for {verse["arabic"]}'))
                except (QuranSura.DoesNotExist, QuranVerse.DoesNotExist) as e:
                    self.stdout.write(self.style.ERROR(f'Error: {e}'))