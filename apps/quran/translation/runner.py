#!/usr/bin/env python3
"""
اسکریپت اجرای ترجمه کلمه به کلمه قرآن کریم
"""

import os
import sys
import argparse
import json
from datetime import datetime
from typing import List, Optional

# اضافه کردن مسیر پروژه
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# تنظیم Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
import django
django.setup()

# Import های مطلق
from apps.quran.translation.config import TranslationConfig
from apps.quran.translation.translator import QuranWordTranslator


def print_banner():
    """نمایش بنر شروع"""
    print("=" * 80)
    print("🕌 مترجم کلمه به کلمه قرآن کریم - زبان آذربایجانی")
    print("📅 تاریخ:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("🤖 مدل: Google Gemini 2.0-flash-lite")
    print("=" * 80)


def print_help():
    """نمایش راهنما"""
    help_text = """
🔧 نحوه استفاده:

python apps/quran/translation/runner.py [گزینه‌ها]

📋 گزینه‌های موجود:
  --test              اجرا در حالت تست (محدود به 50 کلمه)
  --max-words N       حداکثر تعداد کلمات برای پردازش
  --api-keys KEY1,KEY2 لیست کلیدهای API (جدا شده با کاما)
  --stats             نمایش آمار فعلی
  --reset             ریست پیشرفت و شروع مجدد
  --help              نمایش این راهنما

💡 مثال‌ها:
  # اجرا در حالت تست
  python apps/quran/translation/runner.py --test

  # اجرا با کلیدهای API سفارشی
  python apps/quran/translation/runner.py --api-keys "key1,key2,key3"

  # اجرا با محدودیت تعداد کلمات
  python apps/quran/translation/runner.py --max-words 1000

  # نمایش آمار فعلی
  python apps/quran/translation/runner.py --stats

  # ریست و شروع مجدد
  python apps/quran/translation/runner.py --reset

📁 فایل‌های خروجی:
  - main_az.json: فایل ترجمه نهایی
  - translation_progress.json: پیشرفت ترجمه
  - translation_requests.log: لاگ درخواست‌ها
"""
    print(help_text)


def show_stats():
    """نمایش آمار فعلی"""
    print("📊 آمار فعلی ترجمه:")
    print("-" * 40)
    
    # بررسی وجود فایل پیشرفت
    if not os.path.exists(TranslationConfig.PROGRESS_FILE):
        print("❌ هیچ پیشرفتی یافت نشد. ترجمه هنوز شروع نشده است.")
        return
    
    try:
        with open(TranslationConfig.PROGRESS_FILE, 'r', encoding='utf-8') as f:
            progress = json.load(f)
        
        print(f"📈 کل پردازش شده: {progress.get('total_processed', 0):,}")
        print(f"✅ ترجمه موفق: {progress.get('successful_translations', 0):,}")
        print(f"❌ ترجمه ناموفق: {progress.get('failed_translations', 0):,}")
        print(f"🔄 آخرین ایندکس: {progress.get('last_processed_index', -1):,}")
        print(f"🕐 شروع: {progress.get('start_time', 'نامشخص')}")
        print(f"🕑 آخرین به‌روزرسانی: {progress.get('last_update', 'نامشخص')}")
        print(f"🧪 حالت تست: {'بله' if progress.get('test_mode', False) else 'خیر'}")
        
        # محاسبه درصد پیشرفت
        if progress.get('total_processed', 0) > 0:
            # تخمین کل کلمات (73,063)
            total_estimated = 73063
            progress_percent = (progress['total_processed'] / total_estimated) * 100
            print(f"📊 پیشرفت تخمینی: {progress_percent:.2f}%")
        
    except Exception as e:
        print(f"❌ خطا در خواندن آمار: {e}")


def reset_progress():
    """ریست پیشرفت"""
    print("⚠️  آیا مطمئن هستید که می‌خواهید پیشرفت را ریست کنید؟")
    print("   این عمل تمام پیشرفت قبلی را حذف می‌کند.")
    
    confirm = input("   برای تأیید 'yes' تایپ کنید: ").strip().lower()
    
    if confirm == 'yes':
        files_to_remove = [
            TranslationConfig.PROGRESS_FILE,
            TranslationConfig.REQUESTS_LOG_FILE
        ]
        
        removed_count = 0
        for file_path in files_to_remove:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    removed_count += 1
                    print(f"🗑️  حذف شد: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"❌ خطا در حذف {file_path}: {e}")
        
        print(f"✅ {removed_count} فایل حذف شد. ترجمه از ابتدا شروع خواهد شد.")
    else:
        print("❌ ریست لغو شد.")


def parse_api_keys(api_keys_str: str) -> List[str]:
    """تجزیه رشته کلیدهای API"""
    if not api_keys_str:
        return []

    keys = [key.strip() for key in api_keys_str.split(',')]
    return [key for key in keys if key]  # حذف کلیدهای خالی


def retry_failed_translations():
    """بازیابی ترجمه‌های ناموفق"""
    print("🔄 شروع بازیابی ترجمه‌های ناموفق...")
    print("-" * 50)

    config = TranslationConfig()
    translator = QuranWordTranslator(config)

    try:
        result = translator.retry_failed_translations()

        if "error" in result:
            print(f"❌ خطا: {result['error']}")
        elif "message" in result:
            print(f"ℹ️  {result['message']}")
        else:
            print("📊 نتایج بازیابی:")
            print(f"   کل ناموفق: {result['total_failed']}")
            print(f"   بازیابی شده: {result['recovered']}")
            print(f"   باقی‌مانده: {result['remaining_failed']}")

            if result['recovered'] > 0:
                print(f"✅ {result['recovered']} ترجمه با موفقیت بازیابی شد!")

            if result['remaining_failed'] > 0:
                print(f"⚠️  {result['remaining_failed']} ترجمه هنوز ناموفق باقی مانده")

    except Exception as e:
        print(f"❌ خطای غیرمنتظره: {e}")


def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(
        description='مترجم کلمه به کلمه قرآن کریم به زبان آذربایجانی',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--test', action='store_true', 
                       help='اجرا در حالت تست (محدود به 50 کلمه)')
    parser.add_argument('--max-words', type=int, 
                       help='حداکثر تعداد کلمات برای پردازش')
    parser.add_argument('--api-keys', type=str,
                       help='لیست کلیدهای API جدا شده با کاما')
    parser.add_argument('--stats', action='store_true',
                       help='نمایش آمار فعلی')
    parser.add_argument('--reset', action='store_true',
                       help='ریست پیشرفت و شروع مجدد')
    parser.add_argument('--retry-failed', action='store_true',
                       help='بازیابی ترجمه‌های ناموفق')
    parser.add_argument('--help-detailed', action='store_true',
                       help='نمایش راهنمای تفصیلی')
    
    args = parser.parse_args()
    
    # نمایش راهنمای تفصیلی
    if args.help_detailed:
        print_help()
        return
    
    # نمایش آمار
    if args.stats:
        show_stats()
        return
    
    # ریست پیشرفت
    if args.reset:
        reset_progress()
        return

    # بازیابی ترجمه‌های ناموفق
    if args.retry_failed:
        retry_failed_translations()
        return

    # نمایش بنر
    print_banner()
    
    # تجزیه کلیدهای API
    api_keys = None
    if args.api_keys:
        api_keys = parse_api_keys(args.api_keys)
        if not api_keys:
            print("❌ خطا: کلیدهای API نامعتبر")
            return
        print(f"🔑 استفاده از {len(api_keys)} کلید API سفارشی")
    
    # ایجاد مترجم
    try:
        translator = QuranWordTranslator(api_keys=api_keys, test_mode=args.test)
        
        # اجرای ترجمه
        print("\n🚀 شروع فرآیند ترجمه...")
        print("⚠️  برای توقف از Ctrl+C استفاده کنید")
        print("-" * 60)
        
        final_stats = translator.run_translation(max_words=args.max_words)
        
        # نمایش نتایج نهایی
        print("\n" + "=" * 60)
        print("📋 گزارش نهایی:")
        print("-" * 30)
        
        if "error" in final_stats:
            print(f"❌ خطا: {final_stats['error']}")
        else:
            print(f"📖 کل کلمات: {final_stats['total_words']:,}")
            print(f"✅ پردازش شده: {final_stats['processed_words']:,}")
            print(f"🎯 موفق: {final_stats['successful_translations']:,}")
            print(f"❌ ناموفق: {final_stats['failed_translations']:,}")
            print(f"🧪 حالت تست: {'بله' if final_stats['test_mode'] else 'خیر'}")
            print(f"📁 فایل خروجی: {final_stats['output_file']}")
            
            # آمار کلاینت
            client_stats = final_stats.get('client_stats', {})
            if client_stats:
                print(f"\n🔗 آمار API:")
                print(f"   درخواست‌ها: {client_stats.get('total_requests', 0):,}")
                print(f"   نرخ موفقیت: {client_stats.get('success_rate', 0):.1f}%")
                print(f"   چرخش کلید: {client_stats.get('api_key_rotations', 0):,}")
        
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️  ترجمه توسط کاربر متوقف شد")
        print("💾 پیشرفت ذخیره شده است. می‌توانید بعداً ادامه دهید.")
        
    except Exception as e:
        print(f"\n❌ خطای غیرمنتظره: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
