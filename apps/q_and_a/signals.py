import json
from django.utils import timezone
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from django.db import IntegrityError

from apps.account.models import User, ProviderRequest
from apps.meet.models import MeetProviderProfile
from .models import Consultants
from dj_language.models import Language
from filer.models import Image
import logging
from apps.q_and_a.admin.consultant import encrypt_password

# Safe import for translation task
try:
    from apps.command.tasks.model_translation import translate_model_fields_task
except ImportError:
    print("⚠️  Translation task not available - translation will be skipped")
    translate_model_fields_task = None


logger = logging.getLogger(__name__)


@receiver(post_save, sender=ProviderRequest)
def update_talk_request(sender, instance, **kwargs):
    try:
        if (instance.status == ProviderRequest.Status.accepted) and (instance.service == ProviderRequest.Service.talk) and instance.is_custom == True:
            # تلاش برای بازیابی و پردازش داده‌ها
            try:
                saved_data = json.loads(instance.description.split('Data: ')[-1])
            except (json.JSONDecodeError, IndexError) as e:
                logger.error(f"Error decoding JSON or splitting description: {e}")
                return  # بازگشت از سیگنال در صورت خطا

            full_name = saved_data.get('full_name')
            email = saved_data.get('email')
            bio = saved_data.get('bio')
            avatar_image = saved_data.get('avatar')
            birthdate = saved_data.get('birthdate')
            institution_name = saved_data.get('institution_name')
            social_medias = saved_data.get('social_medias')
            user_id = saved_data.get('user_id')
            languages = saved_data.get('languages')
            wa_number = saved_data.get('wa_number')


            user = User.objects.filter(id=user_id).first()
            # base_scheduling_data = json.dumps({
            #     "saturday": [], 
            #     "sunday": [], 
            #     "monday": [], 
            #     "tuesday": [], 
            #     "wednesday": [], 
            #     "thursday": [], 
            #     "friday": []
            # })
            # Encrypt default password "12345678"
            encrypted_password = encrypt_password("12345678")
            try:
                consultant = Consultants.objects.create(
                    user_id=user_id,
                    username=email,
                    password=encrypted_password,  
                    languages=languages,
                    call_languages=languages,
                    email=email,
                    description=bio,
                    fullname=full_name,
                    visible=True,
                    is_banned=False,
                    token=str(user.auth_token),
                    phone=wa_number,
                    is_supporter=False,
                    contact_type=",chat",
                    consultant_type="regular",
                    name_translation=json.dumps({}),
                    topics_translation=json.dumps({}),
                    # scheduling=base_scheduling_data,
                    is_ai=False,
                    fcm=str(user.fcm) if user.fcm else None,
                    birthdate=birthdate if birthdate else None,
                    institution_name=institution_name if institution_name else None,
                    social_medias=social_medias if social_medias else None,
                    avatar_url=avatar_image,
                    created_at=timezone.now(),
                    updated_at=timezone.now(),
                    country= user.country if user and user.country else None,
                    city= user.city if user and user.city else None,
                    status='offline',
                    session_duration=20,
                    video_call_cost=5,
                    voice_call_cost=5
                )
            except IntegrityError as e:
                print(f'errr/ {e}')
                logger.error(f"Error creating Consultant: {e}")
                return  # بازگشت از سیگنال در صورت خطا

            instance.save(update_fields=['public_name'])

            logger.info(f"Consultant {consultant.username} created successfully.")

    except Exception as e:
        print(f'er/ {e}')
        logger.error(f"Unexpected error in update_talk_request signal: {e}")


@receiver(post_save, sender=Consultants)
def auto_translate_consultant(sender, instance, created, **kwargs):
    """
    Signal handler to automatically translate Consultant fields when saved.
    Uses Celery task for background processing.
    """

    # Quick check for content (basic validation)
    has_content = False
    for field_name in ['fullname', 'slogan', 'description', 'first_messages']:
        if field_name == 'first_messages':
            # Check if first_messages has content
            field_data = getattr(instance, field_name, [])
            if field_data and isinstance(field_data, list):
                for item in field_data:
                    if isinstance(item, dict) and item.get('text', '').strip():
                        has_content = True
                        break
        else:
            # Check regular text fields
            field_value = getattr(instance, field_name, '')
            if field_value and field_value.strip():
                has_content = True
                break

        if has_content:
            break

    if not has_content:
        print(f"ℹ️  No translatable content found for Consultant ID {instance.id}")
        return

    # Check if translation task is available
    if translate_model_fields_task is None:
        print(f"⚠️  Translation task not available for Consultant ID {instance.id}")
        return

    try:
        print(f"🚀 Queuing background translation for Consultant ID {instance.id}")

        # Configure field configurations for the task
        field_configs = []

        # Add name_translation field (from fullname)
        if instance.fullname and instance.fullname.strip():
            field_configs.append({
                "field_name": "name_translation",
                "source_field": "fullname",
                "keys": ['text', 'language_code']
            })

        # Add topics_translation field (from slogan)
        if instance.slogan and instance.slogan.strip():
            field_configs.append({
                "field_name": "topics_translation",
                "source_field": "slogan",
                "keys": ['text', 'language_code']
            })

        # Add description_translation field (from description)
        if instance.description and instance.description.strip():
            field_configs.append({
                "field_name": "description_translation",
                "source_field": "description",
                "keys": ['text', 'language_code']
            })

        # Add first_messages field (if has content)
        first_messages = getattr(instance, 'first_messages', [])
        if first_messages and isinstance(first_messages, list):
            for item in first_messages:
                if isinstance(item, dict) and item.get('text', '').strip():
                    field_configs.append({
                        "field_name": "first_messages",
                        "keys": ['text', 'language_code']
                    })
                    break

        if not field_configs:
            print(f"ℹ️  No fields to translate for Consultant ID {instance.id}")
            return

        # Queue Celery task for background processing
        translate_model_fields_task.delay(
            model_name='Consultants',
            app_name='q_and_a',
            object_id=instance.id,
            field_configs=field_configs,
            use_gemini=False,  # Use Gemini for faster translation
            use_mock=False    # Set to True for testing
        )

        print(f"✅ Translation task queued for Consultant ID {instance.id}")

    except Exception as e:
        print(f"❌ Error queuing translation task for Consultant ID {instance.id}: {e}")

